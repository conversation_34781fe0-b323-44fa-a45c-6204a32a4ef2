import AppNumber from "@/components/AppNumber";
import { EOrderSide } from "@/components/OrderForm/OrderFormMobile";
import { DEFAULT_DECIMAL } from "@/constants";
import { RootState } from "@/store";
import { ETradeRole, TUserTrade } from "@/types/trade";
import { formatUnixTimestamp } from "@/utils/format";
import { getSideColor } from "@/utils/helper";
import { useMemo } from "react";
import { useSelector } from "react-redux";
import { useMediaQuery } from "react-responsive";

const TradeItem = ({ trade }: { trade: TUserTrade }) => {
  const isMobile = useMediaQuery({ query: "(max-width: 992px)" });
  const orderSide = trade?.isBuyer ? EOrderSide.BUY : EOrderSide.SELL;

  const pairSettings = useSelector(
    (state: RootState) => state.pairSettings.pairSettings
  );

  const pairSetting = useMemo(() => {
    if (!pairSettings.length) {
      return {} as any;
    }

    return pairSettings?.find(
      (setting) => setting.symbol.toUpperCase() === trade.symbol.toUpperCase()
    );
  }, [pairSettings]);

  if (isMobile) {
    return (
      <div className="border-white-50 border-b px-4 py-2">
        <div>
          <div className="body-md-medium-14">{trade?.symbol}</div>
          <div className="body-sm-regular-12 flex gap-2 capitalize">
            <div style={{ color: getSideColor(orderSide) }}>
              {orderSide?.toLowerCase()}
            </div>
          </div>
        </div>

        <div className="flex items-center justify-between py-3">
          <div className="body-md-regular-14 text-white-500">Time</div>
          <div className="body-md-medium-14">
            {formatUnixTimestamp(trade?.time, "YYYY-MM-DD HH:mm:ss")}
          </div>
        </div>
        <div className="flex items-center justify-between py-3">
          <div className="body-md-regular-14 text-white-500">Price</div>
          <div className="body-md-medium-14">
            <AppNumber
              value={trade.price || "0"}
              decimals={pairSetting?.pricePrecision}
              isFormatLargeNumber={false}
            />
          </div>
        </div>
        <div className="flex items-center justify-between py-3">
          <div className="body-md-regular-14 text-white-500">Filled</div>
          <div className="body-md-medium-14">
            <AppNumber
              value={trade.qty || "0"}
              decimals={pairSetting?.quantityPrecision}
              isFormatLargeNumber={false}
            />
          </div>
        </div>
        <div className="flex items-center justify-between py-3">
          <div className="body-md-regular-14 text-white-500">Fee</div>
          <div className="body-md-medium-14 flex items-center gap-1">
            <AppNumber
              value={trade.fee || "0"}
              decimals={DEFAULT_DECIMAL}
              isFormatLargeNumber={false}
            />
            {trade?.baseSymbol?.toUpperCase()}
          </div>
        </div>
        <div className="flex items-center justify-between py-3">
          <div className="body-md-regular-14 text-white-500">Role</div>
          <div className="body-md-medium-14">
            {trade?.isMaker ? ETradeRole.MAKER : ETradeRole.TAKER}
          </div>
        </div>
        <div className="flex items-center justify-between py-3">
          <div className="body-md-regular-14 text-white-500">Total</div>
          <div className="body-md-medium-14 flex items-center gap-1">
            <AppNumber
              value={trade.quoteQty}
              decimals={10} // Would be 10 for total
              isFormatLargeNumber={false}
            />
            {trade?.quoteSymbol?.toUpperCase()}
          </div>
        </div>
        {/* <div className="flex items-center justify-between py-3">
          <div className="body-md-regular-14 text-white-500">Total in USDT</div>
          <div className="body-md-medium-14 flex items-center gap-1">
            ≈
            <AppNumber
              value={trade.quoteQty}
              decimals={8}
              isFormatLargeNumber={false}
            />
            {trade?.quoteSymbol?.toUpperCase()}
          </div>
        </div> */}
      </div>
    );
  }

  return (
    <div className="border-white-50 hover:bg-white-50 flex w-full items-center border-b">
      <div className="body-sm-regular-12 text-white-1000 flex w-[14%] min-w-[100px] items-center px-2 py-2.5 ">
        {formatUnixTimestamp(trade?.time, "YYYY-MM-DD HH:mm:ss")}
      </div>
      <div className="body-sm-regular-12 text-white-1000 w-[13%] min-w-[100px] px-2 py-2.5 text-left">
        {trade?.symbol}
      </div>
      <div
        className="body-sm-regular-12 w-[13%] min-w-[100px] px-2 py-2.5 text-left capitalize"
        style={{ color: getSideColor(orderSide) }}
      >
        {orderSide?.toLowerCase()}
      </div>
      <div className="body-sm-regular-12 text-white-1000 w-[13%] min-w-[100px] px-2 py-2.5 text-left">
        <AppNumber
          value={trade.price || "0"}
          decimals={pairSetting?.pricePrecision}
          isFormatLargeNumber={false}
        />
      </div>
      <div className="body-sm-regular-12 text-white-1000 w-[13%] min-w-[100px] px-2 py-2.5 text-left">
        <AppNumber
          value={trade.qty || "0"}
          decimals={DEFAULT_DECIMAL}
          isFormatLargeNumber={false}
        />
      </div>
      <div className="body-sm-regular-12 text-white-1000 w-[12%] min-w-[100px] px-2 py-2.5 text-left">
        <AppNumber
          value={trade.fee || "0"}
          decimals={DEFAULT_DECIMAL}
          isFormatLargeNumber={false}
        />
      </div>
      <div className="body-sm-regular-12 text-white-1000 w-[11%] min-w-[100px] px-2 py-2.5 text-left">
        {trade?.isMaker ? ETradeRole.MAKER : ETradeRole.TAKER}
      </div>
      <div className=" item-centers  body-sm-regular-12 text-white-1000 flex w-[11%] min-w-[100px] gap-1 px-2 py-2.5 text-left">
        <AppNumber
          value={trade.quoteQty}
          decimals={DEFAULT_DECIMAL} // Would be 10 for total
          isFormatLargeNumber={false}
        />
        {trade?.quoteSymbol?.toUpperCase()}
      </div>
      {/* <div className="body-sm-regular-12 text-white-1000 flex w-[10%] min-w-[120px] items-center gap-1 px-2 py-2.5 text-left">
        ≈{" "}
        <AppNumber
          value={trade.quoteQty}
          decimals={DEFAULT_DECIMAL}
          isFormatLargeNumber={false}
        />
        {trade?.quoteSymbol?.toUpperCase()}
      </div> */}
    </div>
  );
};

export default TradeItem;
